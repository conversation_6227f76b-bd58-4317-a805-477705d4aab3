#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Python脚本运行功能
"""

import sys
import os
import subprocess
import json

def test_python_command():
    """测试Python命令是否可用"""
    commands = ['python', 'python3', 'py']
    
    for cmd in commands:
        try:
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=5)
            if result.returncode == 0:
                print(f"✓ {cmd} 可用: {result.stdout.strip()}")
                return cmd
            else:
                print(f"✗ {cmd} 不可用")
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            print(f"✗ {cmd} 不可用: {e}")
    
    return None

def test_script_execution():
    """测试脚本执行"""
    script_path = os.path.join('scripts', 'python', '获取当前时间', 'main.py')
    
    if not os.path.exists(script_path):
        print(f"✗ 脚本文件不存在: {script_path}")
        return False
    
    print(f"✓ 脚本文件存在: {script_path}")
    
    # 测试配置编码
    config = {"run_mode": "fast", "max_retries": 5, "enable_logging": True}
    config_json = json.dumps(config)
    import base64
    encoded_config = base64.b64encode(config_json.encode('utf-8')).decode('utf-8')
    
    python_cmd = test_python_command()
    if not python_cmd:
        print("✗ 没有可用的Python命令")
        return False
    
    try:
        script_dir = os.path.dirname(script_path)
        print(f"脚本目录: {script_dir}")
        print(f"执行命令: {python_cmd} {os.path.basename(script_path)} {encoded_config[:50]}...")

        result = subprocess.run([python_cmd, os.path.basename(script_path), encoded_config],
                              capture_output=True,
                              text=True,
                              timeout=10,
                              cwd=script_dir)
        
        print(f"退出码: {result.returncode}")
        print(f"stdout: {result.stdout}")
        if result.stderr:
            print(f"stderr: {result.stderr}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"✗ 执行脚本时出错: {e}")
        return False

if __name__ == "__main__":
    print("=== Python脚本运行测试 ===")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print()
    
    # 测试Python命令
    python_cmd = test_python_command()
    print()
    
    # 测试脚本执行
    success = test_script_execution()
    print()
    
    if success:
        print("✓ 所有测试通过")
    else:
        print("✗ 测试失败")
