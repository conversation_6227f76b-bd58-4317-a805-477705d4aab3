# Script for 123
import sys
import json
import base64

def main(config):
    """
    Main execution function.
    'config' is a dictionary with your script's parameters.
    """
    print(f"--- Running script '{name}' ---")
    print("Received configuration:")
    print(json.dumps(config, indent=2, ensure_ascii=False))
    
    # --- Your script logic starts here ---
    # Example: Accessing a config value
    if config.get('enable_logging'):
        print("\nLogging is enabled.")
    # --- Your script logic ends here ---
    
    print("\n--- Script finished ---")

if __name__ == "__main__":
    # The Base64 encoded config is passed as the first and only argument
    if len(sys.argv) > 1:
        try:
            config_base64 = sys.argv[1]
            config_json_bytes = base64.b64decode(config_base64)
            config_json = config_json_bytes.decode('utf-8')
            config_data = json.loads(config_json)
            main(config_data)
        except Exception as e:
            print(f"Error decoding or parsing config from Base64: {e}", file=sys.stderr)
            sys.exit(1)
    else:
        # Fallback for direct execution without arguments, for testing
        print("Running with default empty config (no arguments provided).")
        main({})
