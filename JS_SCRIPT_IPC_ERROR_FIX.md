# JS脚本IPC错误修复

## 问题描述
在JS脚本页面点击"调试IPC"按钮时出现错误：
```
🔧 正在测试IPC序列化...
❌ IPC调用失败
💬 错误: Error invoking remote method 'js:run-script': [object Object]
✅ 错误对象可序列化
```

## 问题分析

### 🔍 错误根源
1. **Promise错误处理不当**：后端JS脚本执行使用Promise但缺少适当的错误处理
2. **错误对象序列化问题**：复杂的错误对象可能包含不可序列化的属性
3. **IPC通信异常**：前后端通信过程中的数据传递问题
4. **异步操作异常**：Promise reject时的错误对象格式问题

### 📋 错误表现
- 前端显示 `[object Object]` 而不是具体的错误信息
- IPC调用失败但错误信息不明确
- 调试信息显示错误对象可序列化，说明问题在传递过程中

## 修复方案

### 1. 增强Promise错误处理 ⭐
```javascript
// 修复前：缺少外层错误处理
ipcMain.handle('js:run-script', async (event, { scriptName, configValues }) => {
  return new Promise((resolve, reject) => {
    // 脚本执行逻辑...
  })
})

// 修复后：添加try-catch包装
ipcMain.handle('js:run-script', async (event, { scriptName, configValues }) => {
  try {
    return await new Promise((resolve, reject) => {
      // 脚本执行逻辑...
    })
  } catch (error) {
    console.error('JS Script execution error:', error)
    throw createSerializableObject({
      message: `脚本执行异常: ${error.message}`,
      stdout: '',
      stderr: error.message || '',
      originalError: error.message,
      debug: {
        error: error.message,
        stack: error.stack
      }
    })
  }
})
```

### 2. 改进错误对象序列化 ⭐
```javascript
function createSerializableObject(obj) {
  // 处理Error对象的特殊情况
  if (obj instanceof Error) {
    return {
      name: obj.name,
      message: obj.message,
      stack: obj.stack
    };
  }
  
  // 处理其他对象类型...
  if (typeof obj === 'object') {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value instanceof Error) {
        result[key] = {
          name: value.name,
          message: value.message,
          stack: value.stack
        };
      } else {
        result[key] = createSerializableObject(value);
      }
    }
    return result;
  }
}
```

### 3. 增强前端调试信息 ⭐
```javascript
async function testIpcSerialization() {
  try {
    // 添加详细的调试信息
    console.log('=== 测试IPC序列化 ===')
    console.log('选中的脚本:', selectedScript.value)
    console.log('脚本名称:', selectedScript.value.name)
    console.log('配置参数:', config.value.values)
    console.log('配置schema:', config.value.schema)

    // 测试参数序列化
    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      configValues: JSON.parse(JSON.stringify(config.value.values || {}))
    }
    
    try {
      const serialized = JSON.stringify(cleanParams)
      const deserialized = JSON.parse(serialized)
      addLog('✅ 参数对象可序列化\n', false)
      console.log('序列化测试成功:', deserialized)
    } catch (e) {
      addLog(`❌ 参数对象序列化失败: ${e.message}\n`, false)
      console.error('参数序列化失败:', e)
      return
    }

    const result = await window.api.runJsScript(cleanParams)
    // 处理结果...
  } catch (error) {
    // 详细的错误处理...
  }
}
```

### 4. 创建测试脚本 ⭐
为了避免中文字符可能导致的问题，创建了英文名称的测试脚本：

**test-script/main.js**：
```javascript
// Test script
console.log('--- Running test script ---');

// Get configuration from command line arguments
let config = {};
if (process.argv[2]) {
  try {
    const configString = Buffer.from(process.argv[2], 'base64').toString('utf-8');
    config = JSON.parse(configString);
    console.log('Received configuration:');
    console.log(JSON.stringify(config, null, 2));
  } catch (e) {
    console.error('Failed to parse configuration:', e.message);
    process.exit(1);
  }
} else {
  console.log('No configuration provided');
}

console.log('Current time:', new Date().toLocaleString());
console.log('Node.js version:', process.version);
console.log('--- Test script finished ---');
```

**test-script/config.json**：
```json
[
  {
    "name": "test_mode",
    "label": "Test Mode",
    "type": "select",
    "options": ["fast", "slow"],
    "default": "fast"
  },
  {
    "name": "count",
    "label": "Count",
    "type": "number",
    "default": 1
  },
  {
    "name": "enabled",
    "label": "Enabled",
    "type": "checkbox",
    "default": true
  }
]
```

## 技术原理

### 🔄 IPC通信流程
1. **前端调用**：`window.api.runJsScript(cleanParams)`
2. **preload转发**：`ipcRenderer.invoke('js:run-script', data)`
3. **后端处理**：`ipcMain.handle('js:run-script', async (event, params) => {...})`
4. **结果返回**：通过Promise resolve/reject返回结果

### 🛡️ 错误处理层次
```
前端调用
    ↓
preload转发
    ↓
后端IPC处理器 (try-catch包装)
    ↓
Promise执行 (内部错误处理)
    ↓
Node.js进程执行 (spawn错误处理)
    ↓
结果序列化 (createSerializableObject)
    ↓
返回前端
```

### 📊 序列化安全性
- **基本类型**：string, number, boolean 直接返回
- **数组**：递归处理每个元素
- **对象**：遍历属性，特殊处理Error对象
- **Error对象**：提取 name, message, stack 属性
- **Buffer对象**：转换为字符串
- **其他类型**：转换为字符串

## 调试方法

### 🔍 前端调试
```javascript
// 检查参数序列化
console.log('清理后的参数:', cleanParams)
try {
  const serialized = JSON.stringify(cleanParams)
  console.log('序列化成功')
} catch (e) {
  console.error('序列化失败:', e)
}
```

### 🔍 后端调试
```javascript
// 检查接收到的参数
console.log('=== JS Script IPC Handler Called ===')
console.log('Received parameters:', { scriptName, configValues })

// 检查脚本路径
console.log('Script Directory:', scriptDirectory)
console.log('Script Path Exists:', fs.existsSync(scriptPath))
```

### 🔍 Node.js进程调试
```javascript
// 检查Node.js命令可用性
for (const cmd of nodeCommands) {
  try {
    require('child_process').execSync(`${cmd} --version`, { stdio: 'ignore' })
    console.log('Using Node command:', cmd)
    break
  } catch (e) {
    console.log(`Node command '${cmd}' not available`)
  }
}
```

## 预期效果

### ✅ 修复后的表现
- **清晰的错误信息**：显示具体的错误原因而不是 `[object Object]`
- **详细的调试日志**：前端和后端都有完整的调试信息
- **可靠的错误处理**：所有异常都被正确捕获和处理
- **序列化安全**：所有传递的对象都是可序列化的

### 🎯 用户体验改进
- **友好的错误提示**：用户能够理解错误原因
- **调试信息丰富**：开发者可以快速定位问题
- **操作流畅**：IPC调用稳定可靠
- **兼容性好**：支持各种脚本名称和配置

## 总结

通过以下修复措施，彻底解决了JS脚本IPC调用错误：

1. **🛡️ 增强错误处理**：添加try-catch包装和详细的错误信息
2. **🔄 改进序列化**：确保所有对象都可以安全序列化
3. **🔍 增强调试**：添加详细的调试日志和状态检查
4. **🧪 创建测试**：提供标准的测试脚本验证功能

现在JS脚本的IPC调用应该能够正常工作，并提供清晰的错误信息和调试支持！
