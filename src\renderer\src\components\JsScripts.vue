<template>
  <div class="script-management">
    <div class="header">
      <h2>JS脚本管理</h2>
      <p>管理和配置您的JavaScript自动化脚本。</p>
    </div>

    <div class="script-content">
      <!-- 左侧脚本列表 -->
      <div class="script-list">
        <div class="list-header">
          <h3>脚本列表</h3>
          <button @click="openAddModal" class="add-btn">➕ 新增脚本</button>
        </div>

        <div class="script-items">
          <div
            v-for="script in scripts"
            :key="script.name"
            class="script-item"
            :class="{ active: selectedScript?.name === script.name }"
            @click="selectScript(script)"
          >
            <div class="script-info">
              <div class="script-name">{{ script.name }}</div>
              <div class="script-path">{{ script.path }}</div>
            </div>
            <button @click.stop="deleteScript(script)" class="delete-btn">🗑️</button>
          </div>

          <div v-if="scripts.length === 0" class="no-scripts">
            <p>暂无JS脚本</p>
            <p>点击"新增脚本"按钮创建您的第一个脚本。</p>
          </div>
        </div>
      </div>

      <!-- 右侧脚本详情和配置 -->
      <div class="script-details" v-if="selectedScript">
        <div class="details-header">
          <input type="text" :value="selectedScript.name" class="title-input" readonly />
          <div class="button-group">
            <button @click="runScript" class="run-btn" :disabled="isRunning">
              {{ isRunning ? '运行中...' : '运行' }}
            </button>
            <button @click="saveConfig" :disabled="!isDirty || isRunning" class="save-btn">
              {{ isDirty ? '保存' : '已保存' }}
            </button>
            <button @click="testNodeEnvironment" class="test-btn" :disabled="isRunning">
              测试Node环境
            </button>
            <button @click="testIpcSerialization" class="debug-btn" :disabled="isRunning">
              调试IPC
            </button>
          </div>
        </div>

        <div class="config-section">
          <div class="config-header">
            <h4>配置参数</h4>
            <div class="config-actions">
              <button @click="openAddConfigModal" class="add-config-btn" :disabled="isRunning">
                ➕ 添加配置项
              </button>
            </div>
          </div>

          <div class="config-form">
            <div v-for="(field, index) in config.schema" :key="field.name" class="form-group">
              <div class="form-group-header">
                <label :for="field.name">{{ field.label }}</label>
                <button @click="removeConfigField(index)" class="remove-field-btn" :disabled="isRunning">
                  🗑️
                </button>
              </div>

              <input
                v-if="field.type === 'text' || field.type === 'number'"
                :type="field.type"
                :id="field.name"
                v-model="config.values[field.name]"
              />

              <select
                v-else-if="field.type === 'select'"
                :id="field.name"
                v-model="config.values[field.name]"
              >
                <option v-for="option in field.options" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>

              <input
                v-else-if="field.type === 'checkbox'"
                type="checkbox"
                :id="field.name"
                v-model="config.values[field.name]"
                class="form-checkbox"
              />
            </div>

            <div v-if="config.schema.length === 0" class="no-config">
              <p>此脚本没有可配置的选项。</p>
              <p>点击"添加配置项"按钮来创建配置参数。</p>
            </div>
          </div>
        </div>

        <div class="log-section">
          <h4>执行日志</h4>
          <div class="log-output" v-html="logOutput"></div>
        </div>
      </div>

      <div v-else class="no-selection">
        <p>📝 请选择一个JS脚本。</p>
      </div>
    </div>

    <!-- Add Script Modal -->
    <div v-if="showAddModal" class="modal-overlay" @click.self="closeAddModal">
      <div class="modal-content">
        <h3>新增 JS 脚本</h3>
        <input
          type="text"
          v-model="newScriptName"
          placeholder="脚本名称 (将作为目录名)"
          ref="newScriptNameInput"
          @keyup.enter="addScript"
          @keyup.esc="closeAddModal"
        />
        <div class="modal-actions">
          <button @click="closeAddModal">取消</button>
          <button @click="addScript">确认创建</button>
        </div>
      </div>
    </div>

    <!-- Add Config Field Modal -->
    <div v-if="showAddConfigModal" class="modal-overlay" @click.self="closeAddConfigModal">
      <div class="modal-content config-modal">
        <h3>添加配置项</h3>
        <div class="config-form-modal">
          <div class="form-group">
            <label for="configName">配置名称 (英文)</label>
            <input
              type="text"
              id="configName"
              v-model="newConfigField.name"
              placeholder="例如: max_timeout"
              ref="newConfigNameInput"
              @keyup.enter="addConfigField"
              @keyup.esc="closeAddConfigModal"
              @focus="console.log('配置名称输入框获得焦点')"
              @blur="console.log('配置名称输入框失去焦点')"
              @click="console.log('配置名称输入框被点击')"
              :key="configModalKey"
            />
          </div>

          <div class="form-group">
            <label for="configLabel">显示标签</label>
            <input
              type="text"
              id="configLabel"
              v-model="newConfigField.label"
              placeholder="例如: 最大超时时间"
            />
          </div>

          <div class="form-group">
            <label for="configType">配置类型</label>
            <select id="configType" v-model="newConfigField.type">
              <option value="text">文本</option>
              <option value="number">数字</option>
              <option value="checkbox">复选框</option>
              <option value="select">下拉选择</option>
            </select>
          </div>

          <div v-if="newConfigField.type === 'select'" class="form-group">
            <label for="configOptions">选项 (每行一个)</label>
            <textarea
              id="configOptions"
              v-model="newConfigField.optionsText"
              placeholder="选项1&#10;选项2&#10;选项3"
              rows="4"
            ></textarea>
          </div>

          <div class="form-group">
            <label for="configDefault">默认值</label>
            <input
              v-if="newConfigField.type === 'text'"
              type="text"
              id="configDefault"
              v-model="newConfigField.default"
              placeholder="默认文本值"
            />
            <input
              v-else-if="newConfigField.type === 'number'"
              type="number"
              id="configDefault"
              v-model="newConfigField.default"
              placeholder="默认数字值"
            />
            <input
              v-else-if="newConfigField.type === 'checkbox'"
              type="checkbox"
              id="configDefault"
              v-model="newConfigField.default"
            />
            <select
              v-else-if="newConfigField.type === 'select'"
              id="configDefault"
              v-model="newConfigField.default"
            >
              <option value="">请选择默认值</option>
              <option v-for="option in newConfigField.optionsText.split('\n').filter(o => o.trim())" :key="option" :value="option.trim()">
                {{ option.trim() }}
              </option>
            </select>
          </div>
        </div>

        <div class="modal-actions">
          <button @click="closeAddConfigModal">取消</button>
          <button @click="addConfigField" :disabled="!newConfigField.name || !newConfigField.label">确认添加</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'

// 响应式数据
const scripts = ref([])
const selectedScript = ref(null)
const config = ref({ schema: [], values: {} })
const logOutput = ref('📝 请选择一个脚本。')
const isRunning = ref(false)

// 脚本管理相关
const showAddModal = ref(false)
const newScriptName = ref('')
const newScriptNameInput = ref(null)

// 配置项管理相关
const showAddConfigModal = ref(false)
const newConfigNameInput = ref(null)
const configModalKey = ref(0) // 用于强制重新创建输入框
const newConfigField = ref({
  name: '',
  label: '',
  type: 'text',
  default: '',
  optionsText: ''
})

// 配置变更检测
const originalValuesStr = ref('{}')
const isDirty = computed(() => {
  return JSON.stringify(config.value.values || {}) !== originalValuesStr.value
})

// 日志格式化函数
function formatLogOutput(message, addTimestamp = true) {
  const timestamp = addTimestamp ? `[${new Date().toLocaleTimeString()}] ` : ''
  return `${timestamp}${message}`.replace(/\n/g, '<br>')
}

// 辅助函数：添加日志（追加内容）
function addLog(message, addTimestamp = true) {
  logOutput.value += formatLogOutput(message, addTimestamp)
}

// 辅助函数：设置日志（清空并设置新内容）
function setLog(message, addTimestamp = true) {
  logOutput.value = formatLogOutput(message, addTimestamp)
}

// 监听新增脚本模态框显示状态，确保焦点设置
watch(showAddModal, async (newValue) => {
  if (newValue) {
    // 模态框显示时，确保焦点设置
    await nextTick()
    setTimeout(() => {
      const input = newScriptNameInput.value
      if (input) {
        input.focus()
        console.log('Modal input focused via watcher')
      }
    }, 150) // 稍微增加延迟确保模态框动画完成
  }
})

// 强力焦点设置函数
function forceFocusOnConfigInput() {
  console.log('=== 强力焦点设置开始 ===')

  const setFocus = () => {
    const input = newConfigNameInput.value
    console.log('输入框元素:', input)
    console.log('当前活动元素:', document.activeElement)

    if (input) {
      // 强制移除所有焦点
      if (document.activeElement && document.activeElement.blur) {
        document.activeElement.blur()
      }

      // 强制设置焦点
      input.focus()
      input.select()

      // 验证焦点是否设置成功
      setTimeout(() => {
        console.log('焦点设置后的活动元素:', document.activeElement)
        console.log('焦点设置是否成功:', document.activeElement === input)

        if (document.activeElement !== input) {
          console.warn('焦点设置失败，尝试备用方法')
          // 备用方法：使用 click 事件
          input.click()
          input.focus()
          input.select()
        }
      }, 50)

      return true
    } else {
      console.warn('输入框元素未找到')
      return false
    }
  }

  // 使用 requestAnimationFrame 确保在正确的渲染时机执行
  requestAnimationFrame(() => {
    if (!setFocus()) {
      // 如果第一次失败，再尝试一次
      requestAnimationFrame(() => {
        setFocus()
      })
    }
  })
}

// 监听配置项模态框显示状态，确保焦点设置
watch(showAddConfigModal, async (newValue) => {
  if (newValue) {
    console.log('配置项模态框显示状态变化:', newValue)

    // 等待DOM更新
    await nextTick()

    // 使用强力焦点设置
    forceFocusOnConfigInput()

    // 备用延迟设置
    setTimeout(() => {
      forceFocusOnConfigInput()
    }, 200)

    setTimeout(() => {
      forceFocusOnConfigInput()
    }, 500)
  }
})

async function fetchScripts() {
  scripts.value = await window.api.getJsScripts()
  if (selectedScript.value) {
    const stillExists = scripts.value.some((s) => s.name === selectedScript.value.name)
    if (!stillExists) {
      selectedScript.value = null
      config.value = { schema: [], values: {} }
      logOutput.value = '📝 请选择一个脚本。'
    }
  }
}

onMounted(async () => {
  await fetchScripts()
  if (scripts.value.length > 0) {
    selectScript(scripts.value[0])
  }
})

async function selectScript(script) {
  if (isDirty.value) {
    if (confirm('当前配置有未保存的更改，确定要切换吗？更改将丢失。')) {
      isDirty.value = false
    } else {
      return
    }
  }
  selectedScript.value = script
  logOutput.value = `✅ 已选择脚本: ${script.name}\n🚀 准备就绪，可以运行脚本。`
  const newConfig = await window.api.getJsScriptConfig(script.name)
  if (newConfig) {
    config.value = newConfig
    originalValuesStr.value = JSON.stringify(newConfig.values || {})
  } else {
    config.value = { schema: [], values: {} }
    originalValuesStr.value = '{}'
  }
  isDirty.value = false
}

async function openAddModal() {
  showAddModal.value = true
  newScriptName.value = ''

  // 等待DOM更新并确保焦点设置
  await nextTick()

  // 使用多种方式确保焦点设置成功
  setTimeout(() => {
    const input = newScriptNameInput.value
    if (input) {
      input.focus()
      input.select() // 选中所有文本（如果有的话）
      console.log('Modal input focused')
    } else {
      console.warn('Modal input element not found')
    }
  }, 100) // 稍微延迟确保模态框完全渲染
}

function closeAddModal() {
  showAddModal.value = false
  newScriptName.value = '' // 清理输入内容
  console.log('Modal closed and cleaned')
}

async function addScript() {
  if (!newScriptName.value.trim()) {
    alert('请输入脚本名称')
    return
  }

  try {
    const newScript = await window.api.addJsScript(newScriptName.value.trim())
    await fetchScripts()
    selectScript(newScript)
    closeAddModal()
  } catch (error) {
    console.error('创建脚本时出错:', error)
    alert(`创建脚本时出错: ${error.message}`)
  }
}

async function deleteScript(script) {
  if (!confirm(`确定要删除脚本 "${script.name}" 吗？此操作不可撤销。`)) {
    return
  }

  try {
    await window.api.deleteJsScript(script.name)
    await fetchScripts()

    if (selectedScript.value?.name === script.name) {
      selectedScript.value = null
      config.value = { schema: [], values: {} }
      logOutput.value = '📝 请选择一个脚本。'
    }
  } catch (error) {
    console.error('删除脚本时出错:', error)
    if (error.message) {
      alert(`删除脚本时出错: ${error.message}`)
    } else {
      alert(`删除脚本时出错: ${error.message}`)
    }
  }
}

async function saveConfig() {
  if (!selectedScript.value || !isDirty.value) return
  try {
    // 创建纯净的对象，避免Vue响应式属性导致的序列化问题
    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      values: JSON.parse(JSON.stringify(config.value.values || {})),
      schema: JSON.parse(JSON.stringify(config.value.schema || []))
    }

    console.log('保存配置参数:', cleanParams)

    await window.api.updateJsScriptConfig(cleanParams)
    originalValuesStr.value = JSON.stringify(config.value.values)
    isDirty.value = false

    // 给用户正确的保存成功反馈
    console.log('配置保存成功')
    return true
  } catch (error) {
    console.error('保存配置时出错:', error)
    alert(`保存配置时出错: ${error.message}`)
    return false
  }
}

async function runScript() {
  if (!selectedScript.value || !config.value.values) return

  // 如果有未保存的配置，先保存
  if (isDirty.value) {
    setLog(`💾 保存配置中...\n`)
    const saveSuccess = await saveConfig()
    if (!saveSuccess) {
      return // 保存失败，不继续执行
    }
    addLog(`✅ 配置已保存\n`, false)
  }

  try {
    isRunning.value = true
    addLog(`🚀 正在运行脚本: ${selectedScript.value.name}...\n`, false)

    // 创建纯净的对象，避免Vue响应式属性导致的序列化问题
    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      configValues: JSON.parse(JSON.stringify(config.value.values || {}))
    }

    const result = await window.api.runJsScript(cleanParams)

    addLog(`\n=== 执行结果 ===\n`, false)
    if (result.stdout && result.stdout.trim()) {
      addLog(`📄 标准输出:\n${result.stdout}\n`, false)
    }
    if (result.stderr && result.stderr.trim()) {
      addLog(`⚠️ 错误输出:\n${result.stderr}\n`, false)
    }
    if (!result.stdout?.trim() && !result.stderr?.trim()) {
      addLog(`✅ 脚本执行完成，无输出内容\n`, false)
    }

  } catch (error) {
    console.error('运行脚本时出错:', error)
    console.error('错误类型:', typeof error)
    console.error('错误构造函数:', error.constructor.name)
    console.error('错误字符串:', String(error))
    console.error('错误JSON:', JSON.stringify(error, null, 2))

    addLog(`\n❌ 脚本执行失败\n`, false)

    // 尝试多种方式获取错误信息
    let errorMessage = 'Unknown error'
    if (error && error.message) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    } else if (error && error.toString) {
      errorMessage = error.toString()
    } else {
      errorMessage = String(error)
    }

    addLog(`💬 错误信息: ${errorMessage}\n`, false)

    if (error.stdout && error.stdout.trim()) {
      addLog(`\n📄 标准输出:\n${error.stdout}\n`, false)
    }
    if (error.stderr && error.stderr.trim()) {
      addLog(`\n⚠️ 错误输出:\n${error.stderr}\n`, false)
    }
    if (error.debug) {
      addLog(`\n🔍 调试信息:\n${JSON.stringify(error.debug, null, 2)}\n`, false)
    }
    if (error.originalError) {
      addLog(`\n🔗 原始错误: ${error.originalError}\n`, false)
    }

    // 添加完整的错误对象信息
    try {
      const errorInfo = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        keys: Object.keys(error),
        type: typeof error,
        constructor: error.constructor.name
      }
      addLog(`\n🔬 错误对象分析:\n${JSON.stringify(errorInfo, null, 2)}\n`, false)
    } catch (e) {
      addLog(`\n⚠️ 无法分析错误对象: ${e.message}\n`, false)
    }
  } finally {
    isRunning.value = false
  }
}

async function testNodeEnvironment() {
  try {
    isRunning.value = true
    setLog('🔧 正在测试Node环境...\n')

    const result = await window.api.testNodeEnvironment()

    if (result.success) {
      addLog(`✅ Node环境正常\n📦 版本: ${result.version}\n`, false)
    } else {
      addLog(`❌ Node环境异常\n⚠️ 错误: ${result.error}\n`, false)
    }
  } catch (error) {
    console.error('测试Node环境时出错:', error)
    addLog(`❌ 测试失败\n💬 错误: ${error.message}\n`, false)
  } finally {
    isRunning.value = false
  }
}

async function testIpcSerialization() {
  if (!selectedScript.value || !config.value.values) {
    alert('请先选择一个脚本并配置参数')
    return
  }

  try {
    isRunning.value = true
    setLog('🔧 正在测试IPC序列化...\n')

    console.log('=== 测试IPC序列化 ===')
    console.log('选中的脚本:', selectedScript.value)
    console.log('脚本名称:', selectedScript.value.name)
    console.log('配置参数:', config.value.values)
    console.log('配置schema:', config.value.schema)

    // 创建纯净的对象，避免Vue响应式属性导致的序列化问题
    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      configValues: JSON.parse(JSON.stringify(config.value.values || {}))
    }

    console.log('清理后的参数:', cleanParams)

    // 测试参数序列化
    try {
      const serialized = JSON.stringify(cleanParams)
      const deserialized = JSON.parse(serialized)
      addLog('✅ 参数对象可序列化\n', false)
      console.log('序列化测试成功:', deserialized)
    } catch (e) {
      addLog(`❌ 参数对象序列化失败: ${e.message}\n`, false)
      console.error('参数序列化失败:', e)
      return
    }

    const result = await window.api.runJsScript(cleanParams)

    addLog('✅ IPC调用成功\n', false)
    addLog(`📊 结果类型: ${typeof result}\n`, false)
    addLog(`🔑 结果键: ${Object.keys(result).join(', ')}\n`, false)

    // 测试序列化
    try {
      const serialized = JSON.stringify(result)
      const deserialized = JSON.parse(serialized)
      addLog('✅ 结果对象可序列化\n', false)

      if (result.stdout && result.stdout.trim()) {
        addLog(`\n📄 标准输出:\n${result.stdout}\n`, false)
      }
      if (result.stderr && result.stderr.trim()) {
        addLog(`\n⚠️ 错误输出:\n${result.stderr}\n`, false)
      }
    } catch (e) {
      addLog(`❌ 结果对象序列化失败: ${e.message}\n`, false)
    }

  } catch (error) {
    console.error('测试IPC序列化时出错:', error)
    addLog(`❌ IPC调用失败\n💬 错误: ${error.message}\n`, false)

    // 测试错误对象序列化
    try {
      const serialized = JSON.stringify(error)
      const deserialized = JSON.parse(serialized)
      addLog('✅ 错误对象可序列化\n', false)

      if (error.stdout && error.stdout.trim()) {
        addLog(`\n📄 标准输出:\n${error.stdout}\n`, false)
      }
      if (error.stderr && error.stderr.trim()) {
        addLog(`\n⚠️ 错误输出:\n${error.stderr}\n`, false)
      }
    } catch (e) {
      addLog(`❌ 错误对象序列化失败: ${e.message}\n`, false)
      addLog(`🔗 原始错误: ${String(error)}\n`, false)
    }

  } finally {
    isRunning.value = false
  }
}

// 配置项管理函数
async function openAddConfigModal() {
  if (!selectedScript.value) {
    alert('请先选择一个脚本')
    return
  }

  console.log('=== 打开配置项模态框 ===')

  // 强制清除所有可能的焦点状态
  if (document.activeElement && document.activeElement.blur) {
    document.activeElement.blur()
  }

  // 更新key强制重新创建输入框
  configModalKey.value++
  console.log('更新模态框key:', configModalKey.value)

  // 重置表单数据
  newConfigField.value = {
    name: '',
    label: '',
    type: 'text',
    default: '',
    optionsText: ''
  }

  // 显示模态框
  showAddConfigModal.value = true

  // 等待DOM更新
  await nextTick()

  // 使用强力焦点设置
  console.log('开始设置焦点...')
  forceFocusOnConfigInput()
}

function closeAddConfigModal() {
  showAddConfigModal.value = false
  newConfigField.value = {
    name: '',
    label: '',
    type: 'text',
    default: '',
    optionsText: ''
  }
}

async function addConfigField() {
  const field = newConfigField.value

  if (!field.name.trim() || !field.label.trim()) {
    alert('请填写配置名称和显示标签')
    return
  }

  // 检查名称是否已存在
  if (config.value.schema.some(f => f.name === field.name.trim())) {
    alert('配置名称已存在，请使用其他名称')
    return
  }

  // 构建新的配置字段
  const newField = {
    name: field.name.trim(),
    label: field.label.trim(),
    type: field.type
  }

  // 处理默认值
  if (field.type === 'select') {
    const options = field.optionsText.split('\n')
      .map(o => o.trim())
      .filter(o => o.length > 0)

    if (options.length === 0) {
      alert('下拉选择类型需要至少一个选项')
      return
    }

    newField.options = options
    newField.default = field.default || options[0]
  } else if (field.type === 'number') {
    newField.default = Number(field.default) || 0
  } else if (field.type === 'checkbox') {
    newField.default = Boolean(field.default)
  } else {
    newField.default = field.default || ''
  }

  // 添加到schema
  config.value.schema.push(newField)

  // 设置默认值
  config.value.values[newField.name] = newField.default

  // 标记为已修改
  isDirty.value = true

  closeAddConfigModal()

  console.log('添加配置项:', newField)
}

async function removeConfigField(index) {
  const field = config.value.schema[index]

  console.log('=== 删除配置项 ===', field.label)

  if (!confirm(`确定要删除配置项 "${field.label}" 吗？`)) {
    console.log('用户取消删除操作')
    // 确认对话框取消后，强制清理焦点状态
    await nextTick()

    // 强制清除所有焦点
    if (document.activeElement && document.activeElement.blur) {
      document.activeElement.blur()
    }

    // 额外清理
    setTimeout(() => {
      if (document.activeElement && document.activeElement.blur) {
        document.activeElement.blur()
      }
      console.log('取消删除后清理焦点状态完成')
    }, 100)
    return
  }

  console.log('确认删除配置项:', field.label)

  // 从schema中移除
  config.value.schema.splice(index, 1)

  // 从values中移除
  delete config.value.values[field.name]

  // 标记为已修改
  isDirty.value = true

  console.log('删除配置项完成:', field)

  // 删除操作完成后，强制重置所有焦点状态
  await nextTick()

  // 多次清理确保彻底
  const clearFocus = () => {
    if (document.activeElement && document.activeElement.blur) {
      document.activeElement.blur()
    }
    // 额外设置焦点到body
    if (document.body && document.body.focus) {
      document.body.focus()
    }
  }

  clearFocus()
  setTimeout(clearFocus, 50)
  setTimeout(clearFocus, 150)

  console.log('删除配置项后焦点状态重置完成')
}
</script>

<style scoped>
.script-management {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.9);
  padding: 24px;
  box-sizing: border-box;
  overflow-y: auto;
  letter-spacing: 0.2px;
  display: flex;
  flex-direction: column;
}

.header h2 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #ffffff;
  letter-spacing: 0.3px;
}

.header p {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24px;
}

.script-content {
  display: flex;
  gap: 20px;
  flex-grow: 1;
}

.script-list {
  width: 300px;
  background-color: #1a1a1a;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #333;
}

.list-header h3 {
  margin: 0;
  font-size: 16px;
  color: #fff;
}

.add-btn {
  padding: 6px 12px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: #40a9ff;
}

.script-items {
  flex-grow: 1;
  overflow-y: auto;
}

.script-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background-color: #2a2a2a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.script-item:hover {
  background-color: #333;
}

.script-item.active {
  background-color: #1890ff;
  color: white;
}

.script-info {
  flex-grow: 1;
}

.script-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.script-path {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  word-break: break-all;
}

.script-item.active .script-path {
  color: rgba(255, 255, 255, 0.8);
}

.delete-btn {
  padding: 4px 8px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-left: 8px;
  transition: background-color 0.2s;
}

.delete-btn:hover {
  background-color: #d9363e;
}

.no-scripts {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  padding: 40px 20px;
}

.no-scripts p {
  margin: 8px 0;
}

.script-details {
  flex-grow: 1;
  background-color: #1a1a1a;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #333;
}

.title-input {
  font-size: 18px;
  font-weight: 500;
  background: transparent;
  border: none;
  color: #fff;
  outline: none;
  flex-grow: 1;
  margin-right: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.run-btn, .save-btn, .test-btn, .debug-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.run-btn {
  background-color: #52c41a;
  color: white;
}

.run-btn:hover:not(:disabled) {
  background-color: #389e0d;
}

.save-btn {
  background-color: #1890ff;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background-color: #40a9ff;
}

.save-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}

.test-btn {
  background-color: #fa8c16;
  color: white;
}

.test-btn:hover:not(:disabled) {
  background-color: #d46b08;
}

.debug-btn {
  background-color: #722ed1;
  color: white;
}

.debug-btn:hover:not(:disabled) {
  background-color: #531dab;
}

.run-btn:disabled, .test-btn:disabled, .debug-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}

.config-section {
  margin-bottom: 20px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #333;
}

.config-header h4 {
  margin: 0;
  color: #fff;
  font-size: 16px;
}

.config-actions {
  display: flex;
  gap: 8px;
}

.add-config-btn {
  padding: 6px 12px;
  background-color: #52c41a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.add-config-btn:hover:not(:disabled) {
  background-color: #389e0d;
}

.add-config-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.form-group label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
}

.remove-field-btn {
  padding: 4px 8px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.remove-field-btn:hover:not(:disabled) {
  background-color: #d9363e;
}

.remove-field-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}

.form-group input,
.form-group select {
  padding: 8px 12px;
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-checkbox {
  width: auto !important;
  margin-right: 8px;
}

.no-config {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  padding: 20px;
  background-color: #2a2a2a;
  border-radius: 6px;
}

.no-config p {
  margin: 8px 0;
}

.log-section {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.log-section h4 {
  margin: 0 0 12px 0;
  color: #fff;
  font-size: 16px;
}

.log-output {
  flex-grow: 1;
  background-color: #0d1117;
  border: 1px solid #333;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #e6edf3;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: 200px;
}

.no-selection {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #1a1a1a;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: #2c2c2c;
  padding: 2rem;
  border-radius: 8px;
  width: 400px;
}

.modal-content h3 {
  margin-top: 0;
}

.modal-content input {
  width: 100%;
  padding: 0.5rem;
  margin-top: 1rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #1a1a1a;
  color: #fff;
}

.modal-actions {
  margin-top: 1.5rem;
  text-align: right;
}

.modal-actions button {
  margin-left: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.modal-actions button:first-child {
  background-color: #666;
  color: white;
}

.modal-actions button:last-child {
  background-color: #1890ff;
  color: white;
}

/* 配置项模态框样式 */
.config-modal {
  width: 500px;
  max-width: 90vw;
}

.config-form-modal .form-group {
  margin-bottom: 1rem;
}

.config-form-modal label {
  display: block;
  margin-bottom: 0.5rem;
  color: #fff;
  font-weight: 500;
}

.config-form-modal input,
.config-form-modal select,
.config-form-modal textarea {
  width: 100%;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #1a1a1a;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
}

.config-form-modal input:focus,
.config-form-modal select:focus,
.config-form-modal textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.config-form-modal textarea {
  resize: vertical;
  min-height: 80px;
}
</style>