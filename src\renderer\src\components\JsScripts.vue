<template>
  <div class="script-management">
    <div class="header">
      <h2>JS脚本</h2>
      <p>管理和配置您的JavaScript自动化脚本。</p>
    </div>
    <div class="script-content">
      <div class="placeholder">
        <p>JS脚本管理功能开发中...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// JsScripts component
</script>

<style scoped>
.script-management {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.9);
  padding: 24px;
  box-sizing: border-box;
  overflow-y: auto;
  letter-spacing: 0.2px;
  display: flex;
  flex-direction: column;
}

.header h2 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #ffffff;
  letter-spacing: 0.3px;
}

.header p {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24px;
}

.script-content {
  display: flex;
  gap: 20px;
  flex-grow: 1;
}

.placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #1a1a1a;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
}
</style> 