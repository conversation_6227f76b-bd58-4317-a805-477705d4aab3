# Python脚本运行问题诊断指南

## 问题描述
菜单中Python脚本点击运行失败

## 已修复的问题

### 1. 路径问题
**问题**: 在开发环境中，`app.getAppPath()` 返回的路径与实际脚本路径不匹配
**修复**: 
- 在开发环境中使用 `process.cwd()` 作为根路径
- 在生产环境中使用 `app.getAppPath()` 作为根路径

### 2. 调试信息不足
**问题**: 当脚本运行失败时，错误信息不够详细
**修复**: 
- 添加了详细的调试日志输出
- 增强了错误信息显示，包括路径、命令、退出码等

### 3. Python命令兼容性
**问题**: 不同系统可能使用不同的Python命令（python, python3, py）
**修复**: 
- 自动检测可用的Python命令
- 按优先级尝试多个Python命令

## 测试步骤

### 1. 基础环境测试
运行以下命令测试Python环境：
```bash
python --version
python3 --version
py --version
```

### 2. 脚本文件测试
运行测试脚本：
```bash
python test_python_script.py
```

### 3. 应用程序测试
1. 启动应用程序：`npm run dev`
2. 在菜单中点击"脚本管理" -> "Python脚本"
3. 选择一个脚本（如"获取当前时间"）
4. 点击"运行"按钮
5. 查看日志输出区域的结果

## 调试信息

当运行Python脚本时，控制台会输出以下调试信息：
- Script Name: 脚本名称
- App Path: 应用程序路径
- Scripts Root: 脚本根目录
- Python Scripts Path: Python脚本目录
- Script Directory: 具体脚本目录
- Script Path: 脚本文件路径
- Script Path Exists: 脚本文件是否存在
- Config Values: 配置参数
- Using Python command: 使用的Python命令
- Executing command: 执行的完整命令

## 常见问题及解决方案

### 1. 脚本文件未找到
**症状**: 错误信息显示"脚本文件未找到"
**解决方案**: 
- 检查scripts/python目录是否存在
- 检查具体脚本目录下是否有main.py文件
- 确认文件路径中没有特殊字符

### 2. Python命令不可用
**症状**: 错误信息显示"无法启动Python子进程"
**解决方案**: 
- 确保Python已正确安装
- 确保Python命令在系统PATH中
- 尝试重新安装Python或修复PATH环境变量

### 3. 脚本执行失败
**症状**: 脚本启动但退出码非0
**解决方案**: 
- 查看stderr输出了解具体错误
- 检查脚本语法是否正确
- 确认脚本依赖的模块是否已安装

### 4. 配置参数问题
**症状**: 脚本运行但配置参数解析失败
**解决方案**: 
- 检查config.json文件格式是否正确
- 确认数据库中的配置数据是否完整
- 重新保存配置参数

## 手动测试命令

如果应用程序中的脚本运行失败，可以尝试手动运行：

```bash
# 进入脚本目录
cd "scripts/python/获取当前时间"

# 直接运行脚本（无参数）
python main.py

# 使用配置参数运行脚本
python main.py eyJydW5fbW9kZSI6ICJmYXN0IiwgIm1heF9yZXRyaWVzIjogNSwgImVuYWJsZV9sb2dnaW5nIjogdHJ1ZX0=
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 操作系统版本
2. Python版本
3. 应用程序控制台的完整错误日志
4. 手动测试命令的输出结果
