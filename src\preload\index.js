import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

const api = {
  getCmdScripts: () => ipcRenderer.invoke('db:get-cmd-scripts'),
  addCmdScript: (data) => ipcRenderer.invoke('db:add-cmd-script', data),
  updateCmdScript: (data) => ipcRenderer.invoke('db:update-cmd-script', data),
  deleteCmdScript: (id) => ipcRenderer.invoke('db:delete-cmd-script', id),
  runCmdScript: (data) => ipcRenderer.invoke('cmd:run-script', data),
  getScriptLogs: (scriptId) => ipcRenderer.invoke('db:get-script-logs', scriptId),

  // Python Script APIs (Hybrid FS & DB)
  getPythonScripts: () => ipcRenderer.invoke('fs:get-python-scripts'),
  addPythonScript: (name) => ipc<PERSON><PERSON><PERSON>.invoke('fs:add-python-script', name),
  deletePythonScript: (name) => ipcRenderer.invoke('fs:delete-python-script', name),
  getPythonScriptConfig: (scriptName) => ipcRenderer.invoke('db:get-python-script-config', scriptName),
  updatePythonScriptConfig: (data) => ipcRenderer.invoke('db:update-python-script-config', data),
  runPythonScript: (data) => ipcRenderer.invoke('python:run-script', data),

  // JS Script APIs (Hybrid FS & DB)
  getJsScripts: () => ipcRenderer.invoke('fs:get-js-scripts'),
  addJsScript: (name) => ipcRenderer.invoke('fs:add-js-script', name),
  deleteJsScript: (name) => ipcRenderer.invoke('fs:delete-js-script', name),
  getJsScriptConfig: (scriptName) => ipcRenderer.invoke('db:get-js-script-config', scriptName),
  updateJsScriptConfig: (data) => ipcRenderer.invoke('db:update-js-script-config', data),
  runJsScript: (data) => ipcRenderer.invoke('js:run-script', data),

  // Test APIs
  ping: () => ipcRenderer.invoke('ping'),
  testPythonEnvironment: () => ipcRenderer.invoke('test:python-environment'),
  testNodeEnvironment: () => ipcRenderer.invoke('test:node-environment')
}

if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  window.electron = electronAPI
  window.api = api
}
