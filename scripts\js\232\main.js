// Script for 232
const fs = require('fs');
const path = require('path');

// Get configuration from command line arguments
let config = {};
if (process.argv[2]) {
  try {
    const configString = Buffer.from(process.argv[2], 'base64').toString('utf-8');
    config = JSON.parse(configString);
    console.log('--- Running script '232' ---');
    console.log('Received configuration:');
    console.log(JSON.stringify(config, null, 2));
    console.log('');
  } catch (e) {
    console.error('Failed to parse configuration:', e.message);
    process.exit(1);
  }
} else {
  console.log('--- Running script '232' ---');
  console.log('No configuration provided');
  console.log('');
}

// Your script logic here
console.log('Current time:', new Date().toLocaleString());

if (config.enable_logging) {
  console.log('Logging is enabled.');
}

console.log('');
console.log('--- <PERSON><PERSON><PERSON> finished ---');
