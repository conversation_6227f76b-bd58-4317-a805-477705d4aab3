{"name": "electron-qingfeng", "version": "1.0.0", "description": "An Electron application with Vue", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "electron-updater": "^6.3.9", "iconv-lite": "^0.6.3", "sqlite": "^5.1.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@electron-toolkit/eslint-config": "^2.0.0", "@electron-toolkit/eslint-config-prettier": "^3.0.0", "@vitejs/plugin-vue": "^5.2.3", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "vite": "^6.2.6", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3"}}