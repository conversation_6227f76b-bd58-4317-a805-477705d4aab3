<template>
  <div class="python-scripts-container">
    <!-- Left Panel: Script List -->
    <div class="script-list-panel">
      <div class="panel-header">
        <h3>Python 脚本</h3>
        <button @click="openAddModal" class="add-btn">新增脚本</button>
      </div>
      <ul>
        <li
          v-for="script in scripts"
          :key="script.name"
          @click="selectScript(script)"
          :class="{ active: selectedScript && selectedScript.name === script.name }"
        >
          <span>{{ script.name }}</span>
          <button @click.stop="deleteScript(script)" class="delete-btn">删除</button>
        </li>
      </ul>
    </div>

    <!-- Right Panel: Dynamic Config Form & Log Output -->
    <div class="right-panel">
      <div class="script-config-panel" v-if="selectedScript && config.schema">
        <div class="config-header">
          <input type="text" :value="selectedScript.name" class="title-input" readonly />
          <div class="button-group">
            <button @click="runScript" class="run-btn" :disabled="isRunning">
              {{ isRunning ? '运行中...' : '运行' }}
            </button>
            <button @click="saveConfig" :disabled="!isDirty || isRunning" class="save-btn">
              {{ isDirty ? '保存' : '已保存' }}
            </button>
            <button @click="testPythonEnvironment" class="test-btn" :disabled="isRunning">
              测试Python环境
            </button>
            <button @click="testIpcSerialization" class="debug-btn" :disabled="isRunning">
              调试IPC
            </button>
          </div>
        </div>

        <div class="config-form">
          <div v-for="field in config.schema" :key="field.name" class="form-group">
            <label :for="field.name">{{ field.label }}</label>

            <input
              v-if="field.type === 'text' || field.type === 'number'"
              :type="field.type"
              :id="field.name"
              v-model="config.values[field.name]"
            />

            <select
              v-else-if="field.type === 'select'"
              :id="field.name"
              v-model="config.values[field.name]"
            >
              <option v-for="option in field.options" :key="option" :value="option">
                {{ option }}
              </option>
            </select>

            <input
              v-else-if="field.type === 'checkbox'"
              type="checkbox"
              :id="field.name"
              v-model="config.values[field.name]"
              class="form-checkbox"
            />
          </div>
          <div v-if="config.schema.length === 0" class="no-config">
            <p>此脚本没有可配置的选项。</p>
            <p>
              请在 <code>{{ selectedScript.path }}</code> 目录下的
              <code>config.json</code> 文件中定义配置。
            </p>
          </div>
        </div>
      </div>

      <!-- Log Output -->
      <div class="log-output-panel" v-if="selectedScript">
        <h4>运行日志</h4>
        <pre class="log-content">{{ logOutput }}</pre>
      </div>

      <!-- Placeholder for when no script is selected -->
      <div v-if="!selectedScript" class="placeholder">
        <p>请从左侧选择一个脚本，或新增一个脚本。</p>
      </div>
    </div>

    <!-- Add Script Modal -->
    <div v-if="showAddModal" class="modal-overlay" @click.self="closeAddModal">
      <div class="modal-content">
        <h3>新增 Python 脚本</h3>
        <input
          type="text"
          v-model="newScriptName"
          placeholder="脚本名称 (将作为目录名)"
          ref="newScriptNameInput"
        />
        <div class="modal-actions">
          <button @click="closeAddModal">取消</button>
          <button @click="addScript">确认创建</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'

const scripts = ref([])
const selectedScript = ref(null)
const config = ref({ schema: [], values: {} })
const originalValuesStr = ref('{}')
const isDirty = ref(false)
const logOutput = ref('💡 欢迎使用Python脚本管理器\n📝 请选择一个脚本开始使用。')
const isRunning = ref(false)

const showAddModal = ref(false)
const newScriptName = ref('')
const newScriptNameInput = ref(null)

// 辅助函数：格式化日志输出，处理换行符和时间戳
function formatLogOutput(message, addTimestamp = true) {
  const timestamp = addTimestamp ? `[${new Date().toLocaleTimeString()}] ` : ''
  return timestamp + message.replace(/\\n/g, '\n')
}

// 辅助函数：添加日志
function addLog(message, addTimestamp = true) {
  logOutput.value += formatLogOutput(message, addTimestamp)
}

// 辅助函数：设置日志（清空并设置新内容）
function setLog(message, addTimestamp = true) {
  logOutput.value = formatLogOutput(message, addTimestamp)
}

async function fetchScripts() {
  scripts.value = await window.api.getPythonScripts()
  if (selectedScript.value) {
    const stillExists = scripts.value.some((s) => s.name === selectedScript.value.name)
    if (!stillExists) {
      selectedScript.value = null
      config.value = { schema: [], values: {} }
      logOutput.value = '📝 请选择一个脚本。'
    }
  }
}

onMounted(async () => {
  await fetchScripts()
  if (scripts.value.length > 0) {
    selectScript(scripts.value[0])
  }
})

async function selectScript(script) {
  if (isDirty.value) {
    if (confirm('当前配置有未保存的更改，确定要切换吗？更改将丢失。')) {
      isDirty.value = false
    } else {
      return
    }
  }
  selectedScript.value = script
  logOutput.value = `✅ 已选择脚本: ${script.name}\n🚀 准备就绪，可以运行脚本。`
  const newConfig = await window.api.getPythonScriptConfig(script.name)
  if (newConfig) {
    config.value = newConfig
    originalValuesStr.value = JSON.stringify(newConfig.values || {})
  } else {
    config.value = { schema: [], values: {} }
    originalValuesStr.value = '{}'
  }
  isDirty.value = false
}

watch(
  () => config.value.values,
  (newValues) => {
    if (newValues && selectedScript.value) {
      isDirty.value = JSON.stringify(newValues) !== originalValuesStr.value
    }
  },
  { deep: true }
)

async function openAddModal() {
  showAddModal.value = true
  newScriptName.value = ''
  await nextTick()
  newScriptNameInput.value?.focus()
}

function closeAddModal() {
  showAddModal.value = false
}

async function addScript() {
  const name = newScriptName.value.trim()
  if (!name) {
    return alert('脚本名称不能为空')
  }
  if (/[\\\\/:*?"<>|]/.test(name)) {
    return alert('脚本名称包含非法字符。')
  }

  try {
    const newScript = await window.api.addPythonScript(name)
    await fetchScripts()
    const scriptInList = scripts.value.find((s) => s.name === newScript.name)
    if (scriptInList) {
      selectScript(scriptInList)
    }
    closeAddModal()
  } catch (error) {
    console.error('添加脚本时出错:', error)
    alert(`添加脚本时出错: ${error.message}`)
  }
}

async function deleteScript(script) {
  if (confirm(`确定要删除脚本目录 “${script.name}” 吗？此操作不可恢复。`)) {
    try {
      await window.api.deletePythonScript(script.name)
      if (selectedScript.value && selectedScript.value.name === script.name) {
        selectedScript.value = null
        config.value = { schema: [], values: {} }
      }
      await fetchScripts()
      // If no script is selected but there are scripts left, select the first one
      if (!selectedScript.value && scripts.value.length > 0) {
        selectScript(scripts.value[0])
      }
    } catch (error) {
      console.error('删除脚本时出错:', error)
      alert(`删除脚本时出错: ${error.message}`)
    }
  }
}

async function saveConfig() {
  if (!selectedScript.value || !isDirty.value) return
  try {
    await window.api.updatePythonScriptConfig({
      scriptName: selectedScript.value.name,
      values: config.value.values
    })
    originalValuesStr.value = JSON.stringify(config.value.values)
    isDirty.value = false
  } catch (error) {
    console.error('保存配置时出错:', error)
    alert(`保存配置时出错: ${error.message}`)
  }
}

async function runScript() {
  if (!selectedScript.value || !config.value.values) return

  if (isDirty.value) {
    await saveConfig()
  }

  try {
    isRunning.value = true
    setLog(`正在运行脚本: ${selectedScript.value.name}...\n`)

    // 创建纯净的对象，避免Vue响应式属性导致的序列化问题
    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      configValues: JSON.parse(JSON.stringify(config.value.values || {}))
    }

    const result = await window.api.runPythonScript(cleanParams)

    addLog(`\n=== 执行结果 ===\n`, false)
    if (result.stdout && result.stdout.trim()) {
      addLog(`📄 标准输出:\n${result.stdout}\n`, false)
    }
    if (result.stderr && result.stderr.trim()) {
      addLog(`⚠️ 错误输出:\n${result.stderr}\n`, false)
    }
    if (!result.stdout?.trim() && !result.stderr?.trim()) {
      addLog(`✅ 脚本执行完成，无输出内容\n`, false)
    }
  } catch (error) {
    console.error('运行Python脚本时出错:', error)

    addLog(`\n❌ 脚本执行失败\n`, false)
    if (error.name) addLog(`🏷️ 错误类型: ${error.name}\n`, false)
    if (error.message) addLog(`💬 错误信息: ${error.message}\n`, false)
    if (error.exitCode !== undefined) addLog(`🔢 退出码: ${error.exitCode}\n`, false)
    if (error.pythonCmd) addLog(`🐍 Python命令: ${error.pythonCmd}\n`, false)

    if (error.stderr && error.stderr.trim()) {
      addLog(`\n⚠️ 错误输出:\n${error.stderr}\n`, false)
    }
    if (error.stdout && error.stdout.trim()) {
      addLog(`\n📄 标准输出:\n${error.stdout}\n`, false)
    }
    if (error.debug) {
      addLog(`\n🔍 调试信息:\n${JSON.stringify(error.debug, null, 2)}\n`, false)
    }
    if (error.originalError) {
      addLog(`\n🔗 原始错误: ${error.originalError}\n`, false)
    }
  } finally {
    isRunning.value = false
  }
}

async function testPythonEnvironment() {
  try {
    isRunning.value = true
    setLog('🔍 正在测试Python环境...\n')

    const result = await window.api.testPythonEnvironment()

    if (result.success) {
      addLog(`✅ Python环境正常\n🐍 版本: ${result.version}\n`, false)
    } else {
      addLog(`❌ Python环境异常\n⚠️ 错误: ${result.error}\n`, false)
    }
  } catch (error) {
    console.error('测试Python环境时出错:', error)
    addLog(`❌ 测试失败\n💬 错误: ${error.message}\n`, false)
  } finally {
    isRunning.value = false
  }
}

async function testIpcSerialization() {
  if (!selectedScript.value) {
    setLog('⚠️ 请先选择一个脚本\n')
    return
  }

  try {
    isRunning.value = true
    setLog('🔧 正在测试IPC序列化...\n')

    console.log('=== 测试IPC序列化 ===')
    console.log('脚本名称:', selectedScript.value.name)
    console.log('配置参数:', config.value.values)

    // 创建纯净的对象，避免Vue响应式属性导致的序列化问题
    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      configValues: JSON.parse(JSON.stringify(config.value.values || {}))
    }

    console.log('清理后的参数:', cleanParams)

    const result = await window.api.runPythonScript(cleanParams)

    addLog('✅ IPC调用成功\n', false)
    addLog(`📊 结果类型: ${typeof result}\n`, false)
    addLog(`🔑 结果键: ${Object.keys(result).join(', ')}\n`, false)

    // 测试序列化
    try {
      const serialized = JSON.stringify(result)
      const deserialized = JSON.parse(serialized)
      addLog('✅ 结果对象可序列化\n', false)

      if (result.stdout && result.stdout.trim()) {
        addLog(`\n📄 标准输出:\n${result.stdout}\n`, false)
      }
      if (result.stderr && result.stderr.trim()) {
        addLog(`\n⚠️ 错误输出:\n${result.stderr}\n`, false)
      }
    } catch (e) {
      addLog(`❌ 结果对象序列化失败: ${e.message}\n`, false)
    }

  } catch (error) {
    console.error('IPC调用失败:', error)
    addLog('\n❌ IPC调用失败\n', false)
    addLog(`🏷️ 错误类型: ${error.name || 'Unknown'}\n`, false)
    addLog(`💬 错误信息: ${error.message || 'No message'}\n`, false)

    // 测试错误对象序列化
    try {
      const serialized = JSON.stringify(error)
      const deserialized = JSON.parse(serialized)
      addLog('✅ 错误对象可序列化\n', false)

      if (error.stdout && error.stdout.trim()) {
        addLog(`\n📄 标准输出:\n${error.stdout}\n`, false)
      }
      if (error.stderr && error.stderr.trim()) {
        addLog(`\n⚠️ 错误输出:\n${error.stderr}\n`, false)
      }
      if (error.debug) {
        addLog(`\n🔍 调试信息:\n${JSON.stringify(error.debug, null, 2)}\n`, false)
      }

    } catch (e) {
      addLog(`❌ 错误对象序列化失败: ${e.message}\n`, false)
      addLog(`🔗 原始错误: ${String(error)}\n`, false)
    }

  } finally {
    isRunning.value = false
  }
}
</script>

<style scoped>
.python-scripts-container {
  display: flex;
  height: 100%;
  background-color: #1a1a1a;
  color: #fff;
}

.script-list-panel {
  width: 250px;
  border-right: 1px solid #333;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 1rem;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
}

.add-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.script-list-panel ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex-grow: 1;
}

.script-list-panel li {
  padding: 1rem;
  cursor: pointer;
  border-bottom: 1px solid #282828;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.script-list-panel li:hover {
  background-color: #2c2c2c;
}

.script-list-panel li.active {
  background-color: #007bff;
  color: white;
}

.delete-btn {
  background: none;
  border: none;
  color: #ff5555;
  cursor: pointer;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s;
}

.script-list-panel li:hover .delete-btn,
.script-list-panel li.active .delete-btn {
  visibility: visible;
  opacity: 1;
}

.right-panel {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.script-config-panel {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-shrink: 0; /* Prevent this panel from shrinking */
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.title-input {
  font-size: 1.5rem;
  background: transparent;
  border: none;
  color: white;
  font-weight: bold;
}

.button-group button {
  margin-left: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.run-btn {
  background-color: #007bff;
  color: white;
}

.run-btn:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.save-btn {
  background-color: #555;
  color: #ccc;
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.save-btn:not(:disabled) {
  background-color: #28a745;
  color: white;
}

.test-btn {
  padding: 8px 16px;
  background-color: #805ad5;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.test-btn:hover:not(:disabled) {
  background-color: #6b46c1;
}

.test-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}

.debug-btn {
  padding: 8px 16px;
  background-color: #e53e3e;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.debug-btn:hover:not(:disabled) {
  background-color: #c53030;
}

.debug-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}

.config-form {
  overflow-y: auto;
}

.form-group {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 0.5rem;
  color: #ccc;
}

.form-group input,
.form-group select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #2c2c2c;
  color: #fff;
}

.form-checkbox {
  width: 20px;
  height: 20px;
  align-self: flex-start;
}

.no-config {
  text-align: center;
  padding: 2rem;
  color: #888;
}

.log-output-panel {
  flex-grow: 1;
  background-color: #0d1117;
  margin: 0 1rem 1rem 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #30363d;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.log-output-panel h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid #21262d;
  padding-bottom: 0.5rem;
  color: #f0f6fc;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.log-output-panel h4::before {
  content: '📋';
  margin-right: 8px;
}

.log-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  flex-grow: 1;
  overflow-y: auto;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #e6edf3;
  background-color: #010409;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #21262d;
  margin: 0;
  min-height: 150px;
  max-height: 400px;
}

/* 滚动条样式 */
.log-content::-webkit-scrollbar {
  width: 8px;
}

.log-content::-webkit-scrollbar-track {
  background: #21262d;
  border-radius: 4px;
}

.log-content::-webkit-scrollbar-thumb {
  background: #484f58;
  border-radius: 4px;
}

.log-content::-webkit-scrollbar-thumb:hover {
  background: #6e7681;
  font-size: 0.9rem;
  color: #ddd;
}

.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #888;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: #2c2c2c;
  padding: 2rem;
  border-radius: 8px;
  width: 400px;
}

.modal-content h3 {
  margin-top: 0;
}

.modal-content input {
  width: 100%;
  padding: 0.5rem;
  margin-top: 1rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #1a1a1a;
  color: #fff;
}

.modal-actions {
  margin-top: 1.5rem;
  text-align: right;
}

.modal-actions button {
  margin-left: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}
</style> 