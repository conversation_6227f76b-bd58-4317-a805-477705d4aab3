<template>
  <div class="sidebar-menu">
    <div class="logo"> 
      
      <span>清风系统</span>
    </div>
    <div class="menu-container">
      <div v-for="item in menuItems" :key="item.id" class="menu-item-wrapper">
        <div
          class="menu-item"
          :class="{ active: activeMenuItemId === item.id, open: item.open }"
          @click="handleMenuClick(item)"
        >
          <div class="menu-item-content">
            <component :is="item.icon" class="menu-icon" />
            <span class="menu-text">{{ item.text }}</span>
          </div>
          <RightOutlined v-if="item.children" class="arrow-icon" :class="{ rotated: item.open }" />
        </div>
        
        <!-- 二级菜单 -->
        <div v-if="item.children && item.open" class="submenu">
          <div
            v-for="subItem in item.children"
            :key="subItem.id"
            class="submenu-item"
            :class="{ active: activeMenuItemId === subItem.id, open: subItem.open }"
            @click.stop="handleMenuClick(subItem, item)"
          >
            <div class="submenu-item-content">
              <span class="submenu-text">{{ subItem.text }}</span>
            </div>
            <RightOutlined v-if="subItem.children" class="arrow-icon" :class="{ rotated: subItem.open }" />
          </div>
          
          <!-- 三级菜单 -->
          <div v-for="subItem in item.children" :key="`sub-${subItem.id}`">
            <div v-if="subItem.children && subItem.open" class="third-menu">
              <div
                v-for="thirdItem in subItem.children"
                :key="thirdItem.id"
                class="third-menu-item"
                :class="{ active: activeMenuItemId === thirdItem.id }"
                @click.stop="handleMenuClick(thirdItem, subItem)"
              >
                <span class="third-menu-text">{{ thirdItem.text }}</span>
              </div>
              
              <!-- 四级菜单 -->
              <div v-for="thirdItem in subItem.children" :key="`third-${thirdItem.id}`">
                <div v-if="thirdItem.children && thirdItem.open" class="fourth-menu">
                  <div
                    v-for="fourthItem in thirdItem.children"
                    :key="fourthItem.id"
                    class="fourth-menu-item"
                    :class="{ active: activeMenuItemId === fourthItem.id }"
                    @click.stop="handleMenuClick(fourthItem, thirdItem)"
                  >
                    <span class="fourth-menu-text">{{ fourthItem.text }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sidebar-footer">
      <div class="current-time">{{ currentTime }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, h, onMounted, onUnmounted } from 'vue';
import {
  DashboardOutlined,
  SettingOutlined,
  RightOutlined,
  ClockCircleOutlined,
  AppstoreOutlined,
  CodeOutlined
} from '@ant-design/icons-vue';

const emit = defineEmits(['menu-item-clicked']);

const activeMenuItemId = ref('dashboard');
const currentTime = ref('00:00:00');

// 菜单项数据
const menuItems = ref([
  { id: 'dashboard', text: '工作台', icon: DashboardOutlined, children: null, open: false },
  { id: 'scripts', text: '脚本管理', icon: CodeOutlined, open: false, children: [
    { id: 'python-scripts', text: 'Python脚本' },
    { id: 'js-scripts', text: 'JS脚本' },
    { id: 'cmd-scripts', text: 'CMD脚本' },
  ]},
  { id: 'service', text: '服务管理', icon: AppstoreOutlined, open: false, children: [
    { id: 'hotkeys', text: '快捷键' },
    { id: 'schedule', text: '定时任务' },
  ]},
  { id: 'settings', text: '系统设置', icon: SettingOutlined, children: null, open: false },
]);

// 更新时间的函数
const updateTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  currentTime.value = `${hours}:${minutes}:${seconds}`;
};

// 定时器引用
let timeInterval;

// 组件挂载时启动时间更新
onMounted(() => {
  updateTime(); // 立即更新一次
  timeInterval = setInterval(updateTime, 1000); // 每秒更新一次
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

const handleMenuClick = (item, parent = null) => {
  if (item.children) {
    item.open = !item.open;
  } else {
    activeMenuItemId.value = item.id;
    emit('menu-item-clicked', item.id);
    
    if (!parent) {
        menuItems.value.forEach(i => {
            if (i.id !== item.id && i.children) {
                i.open = false;
            }
        });
    }
  }
};
</script>

<style scoped>
.sidebar-menu {
  display: flex;
  flex-direction: column;
  height: 100%;
  user-select: none;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  color: white;
  letter-spacing: 0.5px; 
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.menu-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 44px;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.2s;
  font-weight: 450;
  letter-spacing: 0.2px;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.menu-item.active {
  background-color: #1890ff;
  color: white;
}

.menu-item-content {
  display: flex;
  align-items: center;
}

.menu-icon {
  font-size: 16px;
  margin-right: 10px;
}

.menu-text {
  font-size: 14px;
}

.arrow-icon {
  font-size: 12px;
  transition: transform 0.3s;
}

.arrow-icon.rotated {
  transform: rotate(90deg);
}

.submenu {
  background-color: rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.submenu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px 0 42px;
  height: 40px;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.75);
  transition: all 0.2s;
  font-weight: 450;
}

.submenu-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.submenu-item.active {
  color: #1890ff;
}

.submenu-item-content {
  display: flex;
  align-items: center;
}

.submenu-text {
  font-size: 13.5px;
}

.third-menu {
  background-color: rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.third-menu-item {
  display: flex;
  align-items: center;
  padding: 0 16px 0 60px;
  height: 36px;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.2s;
}

.third-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.third-menu-item.active {
  color: #1890ff;
}

.third-menu-text {
  font-size: 13px;
}

.fourth-menu {
  background-color: rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.fourth-menu-item {
  display: flex;
  align-items: center;
  padding: 0 16px 0 76px;
  height: 34px;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.65);
  transition: all 0.2s;
}

.fourth-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.fourth-menu-item.active {
  color: #1890ff;
}

.fourth-menu-text {
  font-size: 12.5px;
}

.sidebar-footer {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
}

.current-time {
  font-family: monospace;
  letter-spacing: 0.5px;
}
</style> 