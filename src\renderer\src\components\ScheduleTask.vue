<template>
  <div class="schedule-task">
    <div class="header">
      <h2>定时任务</h2>
      <p>设置自动执行的任务，可以指定执行时间和重复规则。</p>
    </div>
    
    <!-- 任务列表 -->
    <div class="task-container">
      <div class="task-header">
        <h3>任务列表</h3>
        <button class="add-btn" @click="openTaskForm(null)">新建任务</button>
      </div>
      
      <div class="task-list">
        <div v-if="tasks.length === 0" class="empty-state">
          <p>暂无定时任务，点击"新建任务"创建</p>
        </div>
        <div v-else v-for="task in tasks" :key="task.id" class="task-item">
          <div class="task-info">
            <div class="task-name">{{ task.name }}</div>
            <div class="task-schedule">{{ formatSchedule(task) }}</div>
            <div class="task-desc">{{ task.description }}</div>
          </div>
          <div class="task-actions">
            <div class="toggle-switch-placeholder" :class="{ active: task.active }" @click="toggleTask(task)">
              <div class="switch"></div>
            </div>
            <button class="edit-btn" @click="openTaskForm(task)">编辑</button>
            <button class="delete-btn" @click="confirmDelete(task)">删除</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 任务表单对话框 -->
    <div class="modal" v-if="showTaskForm" @click.self="cancelForm">
      <div class="modal-content">
        <h3>{{ isEditing ? '编辑任务' : '新建任务' }}</h3>
        <form @submit.prevent="saveTask">
          <div class="form-group">
            <label for="taskName">任务名称</label>
            <input type="text" id="taskName" v-model="currentTask.name" required>
          </div>
          
          <div class="form-group">
            <label for="taskDesc">任务描述</label>
            <textarea id="taskDesc" v-model="currentTask.description" rows="2"></textarea>
          </div>
          
          <div class="form-row">
            <div class="form-group half">
              <label for="taskType">任务类型</label>
              <select id="taskType" v-model="currentTask.type">
                <option value="script">脚本执行</option>
                <option value="backup">数据备份</option>
                <option value="notification">通知提醒</option>
              </select>
            </div>
            
            <div class="form-group half">
              <label for="taskPriority">优先级</label>
              <select id="taskPriority" v-model="currentTask.priority">
                <option value="high">高</option>
                <option value="medium">中</option>
                <option value="low">低</option>
              </select>
            </div>
          </div>
          
          <div class="form-group">
            <label>执行时间</label>
            <div class="schedule-options">
              <div class="schedule-option">
                <input type="radio" id="scheduleOnce" value="once" v-model="currentTask.scheduleType">
                <label for="scheduleOnce">单次执行</label>
              </div>
              <div class="schedule-option">
                <input type="radio" id="scheduleDaily" value="daily" v-model="currentTask.scheduleType">
                <label for="scheduleDaily">每天</label>
              </div>
              <div class="schedule-option">
                <input type="radio" id="scheduleWeekly" value="weekly" v-model="currentTask.scheduleType">
                <label for="scheduleWeekly">每周</label>
              </div>
            </div>
          </div>
          
          <div class="form-row" v-if="currentTask.scheduleType === 'once'">
            <div class="form-group">
              <label for="taskDate">日期</label>
              <input type="date" id="taskDate" v-model="currentTask.date">
            </div>
            <div class="form-group">
              <label for="taskTime">时间</label>
              <input type="time" id="taskTime" v-model="currentTask.time">
            </div>
          </div>
          
          <div class="form-group" v-if="currentTask.scheduleType === 'daily'">
            <label for="dailyTime">每天执行时间</label>
            <input type="time" id="dailyTime" v-model="currentTask.time">
          </div>
          
          <div class="form-group" v-if="currentTask.scheduleType === 'weekly'">
            <label>每周执行日</label>
            <div class="weekday-selector">
              <div v-for="(day, index) in weekdays" :key="index" 
                   class="weekday" 
                   :class="{ selected: currentTask.weekdays.includes(index) }"
                   @click="toggleWeekday(index)">
                {{ day }}
              </div>
            </div>
            <div class="form-group" v-if="currentTask.scheduleType === 'weekly'">
              <label for="weeklyTime">执行时间</label>
              <input type="time" id="weeklyTime" v-model="currentTask.time">
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" class="cancel-btn" @click="cancelForm">取消</button>
            <button type="submit" class="save-btn">保存</button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- 删除确认对话框 -->
    <div class="modal" v-if="showDeleteConfirm" @click.self="cancelDelete">
      <div class="modal-content delete-confirm">
        <h3>确认删除</h3>
        <p>您确定要删除任��� "{{ taskToDelete?.name }}" 吗？此操作不可撤销。</p>
        <div class="form-actions">
          <button class="cancel-btn" @click="cancelDelete">取消</button>
          <button class="delete-confirm-btn" @click="deleteTask">删除</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 示例任务数据
const tasks = ref([
  {
    id: 1,
    name: '系统自动备份',
    description: '每周自动备份系统数据，保存到指定位置',
    type: 'backup',
    priority: 'high',
    scheduleType: 'weekly',
    weekdays: [1, 4], // 周一和周四
    time: '03:00',
    active: true
  },
  {
    id: 2,
    name: '日志清理',
    description: '每天凌晨清理过期日志文件',
    type: 'script',
    priority: 'medium',
    scheduleType: 'daily',
    time: '02:30',
    active: true
  },
  {
    id: 3,
    name: '系统更新检查',
    description: '检查系统更新并提醒',
    type: 'notification',
    priority: 'low',
    scheduleType: 'once',
    date: '2023-12-31',
    time: '12:00',
    active: false
  }
]);

// 表单状态
const showTaskForm = ref(false);
const isEditing = ref(false);
const currentTask = reactive({
  id: null,
  name: '',
  description: '',
  type: 'script',
  priority: 'medium',
  scheduleType: 'once',
  date: '',
  time: '',
  weekdays: [],
  active: true
});

// 删除确认状态
const showDeleteConfirm = ref(false);
const taskToDelete = ref(null);

// 星期几的标签
const weekdays = ['日', '一', '二', '三', '四', '五', '六'];

// 格式化任务执行时间的显示
const formatSchedule = (task) => {
  if (task.scheduleType === 'once') {
    return `单次：${task.date} ${task.time}`;
  } else if (task.scheduleType === 'daily') {
    return `每天：${task.time}`;
  } else if (task.scheduleType === 'weekly') {
    const days = task.weekdays.map(d => `周${weekdays[d]}`).join('、');
    return `每周${days}：${task.time}`;
  }
  return '';
};

// 打开任务表单（新建或编辑）
const openTaskForm = (task) => {
  if (task) {
    // 编辑现有任务
    isEditing.value = true;
    Object.assign(currentTask, JSON.parse(JSON.stringify(task)));
  } else {
    // 新建任务
    isEditing.value = false;
    Object.assign(currentTask, {
      id: null,
      name: '',
      description: '',
      type: 'script',
      priority: 'medium',
      scheduleType: 'once',
      date: new Date().toISOString().split('T')[0], // 今天的日期
      time: '12:00',
      weekdays: [],
      active: true
    });
  }
  showTaskForm.value = true;
};

// 取消表单
const cancelForm = () => {
  showTaskForm.value = false;
};

// 保存任务
const saveTask = () => {
  if (isEditing.value) {
    // 更新现有任务
    const index = tasks.value.findIndex(t => t.id === currentTask.id);
    if (index !== -1) {
      tasks.value[index] = { ...currentTask };
    }
  } else {
    // 创建新任务
    const newId = Math.max(0, ...tasks.value.map(t => t.id)) + 1;
    tasks.value.push({
      ...currentTask,
      id: newId
    });
  }
  showTaskForm.value = false;
};

// 切换任务启用状态
const toggleTask = (task) => {
  task.active = !task.active;
};

// 切换周几选择
const toggleWeekday = (dayIndex) => {
  const index = currentTask.weekdays.indexOf(dayIndex);
  if (index === -1) {
    currentTask.weekdays.push(dayIndex);
  } else {
    currentTask.weekdays.splice(index, 1);
  }
};

// 确认删除
const confirmDelete = (task) => {
  taskToDelete.value = task;
  showDeleteConfirm.value = true;
};

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false;
  taskToDelete.value = null;
};

// 执行删除
const deleteTask = () => {
  if (taskToDelete.value) {
    const index = tasks.value.findIndex(t => t.id === taskToDelete.value.id);
    if (index !== -1) {
      tasks.value.splice(index, 1);
    }
  }
  showDeleteConfirm.value = false;
  taskToDelete.value = null;
};
</script>

<style scoped>
.schedule-task {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.9);
  padding: 24px;
  box-sizing: border-box;
  overflow-y: auto;
  letter-spacing: 0.2px;
}

.header h2 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #ffffff;
  letter-spacing: 0.3px;
}

.header p {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24px;
}

.task-container {
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.task-header h3 {
  font-size: 15px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.add-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 6px 14px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: #40a9ff;
}

.task-list {
  padding: 8px 0;
}

.empty-state {
  padding: 32px 16px;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.task-item:last-child {
  border-bottom: none;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 14.5px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.task-schedule {
  font-size: 13px;
  color: #1890ff;
  margin-bottom: 4px;
}

.task-desc {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
}

.task-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.edit-btn, .delete-btn {
  background-color: #2c2c2c;
  border: 1px solid #3a3a3a;
  color: rgba(255, 255, 255, 0.9);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-btn:hover {
  background-color: #3a3a3a;
}

.delete-btn {
  color: #ff4d4f;
  border-color: #ff4d4f;
  background-color: transparent;
}

.delete-btn:hover {
  background-color: rgba(255, 77, 79, 0.1);
}

/* 开关样式 */
.toggle-switch-placeholder {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.toggle-switch-placeholder .switch {
  width: 38px;
  height: 20px;
  background-color: #3a3a3a;
  border-radius: 10px;
  position: relative;
  transition: background-color 0.2s;
}

.toggle-switch-placeholder .switch::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #ffffff;
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.toggle-switch-placeholder.active .switch {
  background-color: #1890ff;
}

.toggle-switch-placeholder.active .switch::before {
  transform: translateX(18px);
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #1e1e1e;
  border-radius: 8px;
  padding: 24px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.modal-content h3 {
  font-size: 16px;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 20px;
  color: #ffffff;
}

.delete-confirm {
  width: 400px;
}

.delete-confirm p {
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.7);
}

/* 表单样式 */
.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group.half {
  flex: 1;
}

label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
}

input[type="text"], 
input[type="date"], 
input[type="time"],
textarea,
select {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #2c2c2c;
  border: 1px solid #3a3a3a;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  box-sizing: border-box;
}

textarea {
  resize: vertical;
}

.schedule-options {
  display: flex;
  gap: 16px;
}

.schedule-option {
  display: flex;
  align-items: center;
  gap: 6px;
}

.weekday-selector {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.weekday {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #2c2c2c;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
}

.weekday.selected {
  background-color: #1890ff;
  color: white;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancel-btn {
  background-color: transparent;
  border: 1px solid #3a3a3a;
  color: rgba(255, 255, 255, 0.7);
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.save-btn {
  background-color: #1890ff;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-confirm-btn {
  background-color: #ff4d4f;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.save-btn:hover {
  background-color: #40a9ff;
}

.delete-confirm-btn:hover {
  background-color: #ff7875;
}
</style> 