<template>
  <div class="dashboard">
    <div class="welcome-container">
      <h2 class="welcome-title">欢迎使用 清风智检</h2>
      <p class="welcome-subtitle">开始您的第一次智能检测</p>
      <img src="../assets/wavy-lines.svg" alt="Welcome background" class="welcome-bg">
    </div>
  </div>
</template>

<script setup>
// Dashboard component
</script>

<style scoped>
.dashboard {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.welcome-container {
  text-align: center;
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: 28px;
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 14px;
  letter-spacing: 0.3px;
}

.welcome-subtitle {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  letter-spacing: 0.2px;
}

.welcome-bg {
  position: absolute;
  bottom: -15%;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 800px;
  opacity: 0.2;
  z-index: 0;
}
</style> 