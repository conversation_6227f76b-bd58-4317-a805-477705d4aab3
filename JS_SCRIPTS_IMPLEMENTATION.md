# JS脚本管理功能实现

## 功能概述
完全基于Python脚本页面创建的JS脚本管理功能，提供与Python脚本相同的功能体验，包括脚本管理、配置管理、执行和调试。

## 核心功能

### 🚀 脚本管理
- **新增脚本**：创建新的JS脚本目录和文件
- **删除脚本**：删除脚本目录和数据库记录
- **脚本列表**：显示所有可用的JS脚本
- **脚本选择**：点击选择要操作的脚本

### ⚙️ 配置管理
- **动态配置项**：支持添加和删除配置参数
- **多种类型**：文本、数字、复选框、下拉选择
- **实时保存**：配置变更自动检测和保存
- **默认值**：每个配置项都可以设置默认值

### 🔧 脚本执行
- **一键运行**：点击运行按钮执行脚本
- **配置传递**：自动将配置参数传递给脚本
- **实时日志**：显示脚本执行的输出和错误信息
- **状态管理**：运行状态指示和按钮禁用

### 🛠️ 调试功能
- **Node环境测试**：检测Node.js是否正确安装
- **IPC调试**：测试前后端通信和数据序列化
- **详细日志**：完整的执行过程日志记录

## 技术架构

### 📁 文件结构
```
scripts/js/
├── 示例脚本/
│   ├── main.js          # 主脚本文件
│   └── config.json      # 配置schema定义
└── [其他脚本目录]/
```

### 🗄️ 数据库表
```sql
CREATE TABLE js_script_configs (
  script_name TEXT PRIMARY KEY,
  config_schema TEXT,      -- JSON格式的配置schema
  config_values TEXT       -- JSON格式的配置值
);
```

### 🔄 API接口
**前端API (preload.js)**：
- `getJsScripts()` - 获取脚本列表
- `addJsScript(name)` - 添加新脚本
- `deleteJsScript(name)` - 删除脚本
- `getJsScriptConfig(scriptName)` - 获取脚本配置
- `updateJsScriptConfig(data)` - 更新脚本配置
- `runJsScript(data)` - 执行脚本
- `testNodeEnvironment()` - 测试Node环境

**后端API (main/index.js)**：
- `fs:get-js-scripts` - 扫描文件系统获取脚本
- `fs:add-js-script` - 创建脚本目录和文件
- `fs:delete-js-script` - 删除脚本目录
- `db:get-js-script-config` - 从数据库获取配置
- `db:update-js-script-config` - 更新数据库配置
- `js:run-script` - 执行JS脚本
- `test:node-environment` - 测试Node环境

## 脚本执行机制

### 📋 配置传递
```javascript
// 配置通过base64编码的JSON字符串传递
const encodedConfig = Buffer.from(JSON.stringify(configValues)).toString('base64')
const nodeProcess = spawn('node', [scriptPath, encodedConfig])
```

### 📝 脚本模板
```javascript
// 脚本接收配置的标准模式
let config = {};
if (process.argv[2]) {
  try {
    const configString = Buffer.from(process.argv[2], 'base64').toString('utf-8');
    config = JSON.parse(configString);
    console.log('Received configuration:', config);
  } catch (e) {
    console.error('Failed to parse configuration:', e.message);
    process.exit(1);
  }
}

// 使用配置进行脚本逻辑
if (config.enable_logging) {
  console.log('Logging is enabled');
}
```

### 🔍 环境检测
```javascript
// 自动检测可用的Node.js命令
const nodeCommands = ['node', 'nodejs']
let nodeCmd = null

for (const cmd of nodeCommands) {
  try {
    require('child_process').execSync(`${cmd} --version`, { stdio: 'ignore' })
    nodeCmd = cmd
    break
  } catch (e) {
    // 继续尝试下一个命令
  }
}
```

## 用户界面

### 🎨 界面布局
- **左侧脚本列表**：显示所有JS脚本，支持选择和删除
- **右侧详情区域**：显示选中脚本的配置和执行界面
- **配置参数区**：动态配置项管理和编辑
- **执行日志区**：实时显示脚本执行输出

### 🎯 交互特性
- **响应式设计**：适配不同屏幕尺寸
- **实时反馈**：操作状态和结果即时显示
- **键盘支持**：支持Enter提交、Esc取消等快捷键
- **焦点管理**：模态框自动获得焦点，支持键盘导航

### 🎪 视觉效果
- **深色主题**：与整体应用风格一致
- **状态指示**：按钮状态、运行状态清晰可见
- **语法高亮**：日志输出使用等宽字体
- **动画过渡**：平滑的界面切换效果

## 配置系统

### 📊 配置类型
```javascript
// 文本类型
{ name: 'api_key', label: 'API密钥', type: 'text', default: '' }

// 数字类型
{ name: 'timeout', label: '超时时间', type: 'number', default: 30 }

// 复选框类型
{ name: 'enable_cache', label: '启用缓存', type: 'checkbox', default: true }

// 下拉选择类型
{ 
  name: 'log_level', 
  label: '日志级别', 
  type: 'select', 
  options: ['debug', 'info', 'warn', 'error'], 
  default: 'info' 
}
```

### 🔄 配置流程
1. **加载配置**：从数据库加载现有配置或从config.json创建默认配置
2. **编辑配置**：用户通过界面修改配置值
3. **变更检测**：实时检测配置是否有未保存的变更
4. **保存配置**：将配置schema和values同时保存到数据库
5. **传递配置**：执行脚本时将配置作为参数传递

## 错误处理

### 🛡️ 异常处理
- **文件不存在**：脚本文件缺失时的友好提示
- **Node环境异常**：Node.js未安装或版本不兼容的处理
- **配置错误**：配置解析失败的错误提示
- **执行失败**：脚本运行错误的详细信息显示

### 🔍 调试支持
- **详细日志**：开发模式下的完整调试信息
- **IPC测试**：前后端通信问题的诊断工具
- **环境检测**：Node.js环境的状态检查
- **序列化测试**：数据传递问题的排查工具

## 示例脚本

### 📝 默认脚本内容
创建新脚本时自动生成包含以下功能的示例代码：
- 配置参数接收和解析
- 基本的脚本逻辑示例
- 错误处理和日志输出
- Node.js环境信息显示

### ⚙️ 默认配置项
- **运行模式**：fast/slow/debug选择
- **最大重试次数**：数字输入
- **启用日志**：复选框
- **输出格式**：json/xml/csv选择
- **超时时间**：数字输入（秒）

## 兼容性

### ✅ 支持环境
- **Node.js版本**：支持所有现代Node.js版本
- **操作系统**：Windows、macOS、Linux
- **Electron**：与当前Electron版本完全兼容
- **浏览器内核**：基于Chromium的现代特性

### 🔄 向后兼容
- **配置迁移**：自动处理配置格式变更
- **脚本兼容**：支持不同版本的脚本格式
- **数据库升级**：平滑的数据库schema升级

## 总结

JS脚本管理功能提供了与Python脚本完全一致的用户体验：
- 🎯 **功能完整**：脚本管理、配置管理、执行调试一应俱全
- 🚀 **性能优秀**：快速的脚本执行和响应式界面
- 🛠️ **易于使用**：直观的界面设计和友好的错误提示
- 🔧 **高度可配置**：灵活的配置系统支持各种脚本需求

现在用户可以像管理Python脚本一样管理和执行JavaScript脚本，享受统一的开发体验！
