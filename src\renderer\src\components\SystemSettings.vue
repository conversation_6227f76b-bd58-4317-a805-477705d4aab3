<template>
  <div class="system-settings">
    <div class="header">
      <h2>系统设置</h2>
      <p>在这里可以配置应用的基础行为和外观。</p>
    </div>
    <div class="settings-list">
      <div v-for="item in settings" :key="item.id" class="setting-item">
        <div class="setting-info">
          <span class="setting-name">{{ item.name }}</span>
          <span class="setting-desc">{{ item.description }}</span>
        </div>
        <div class="setting-control">
          <button v-if="item.type === 'button'">{{ item.controlLabel }}</button>
          <div v-if="item.type === 'toggle'" class="toggle-switch-placeholder" :class="{ active: item.active }">
            <div class="switch"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const settings = ref([
  { id: 'auto-launch', name: '开机自启', description: '应用将在您登录 Windows 后自动启动。', type: 'toggle', active: true },
  { id: 'update-check', name: '检查更新', description: '检查是否有新版本的应用程序。', type: 'button', controlLabel: '立即检查' },
  { id: 'theme', name: '主题设置', description: '切换应用的外观（功能待定）。', type: 'button', controlLabel: '切换模式' },
  { id: 'language', name: '语言选择', description: '更改应用的显示语言（功能待定）。', type: 'button', controlLabel: '选择语言' },
]);
</script>

<style scoped>
.system-settings {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.9); /* 提高对比度 */
  padding: 24px;
  box-sizing: border-box;
  overflow-y: auto;
  letter-spacing: 0.2px;
}

.header h2 {
  font-size: 18px; /* 增大标题 */
  font-weight: 500;
  margin-bottom: 10px;
  color: #ffffff;
  letter-spacing: 0.3px;
}

.header p {
  font-size: 14px; /* 增大说明文本 */
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7); /* 提高对比度 */
  margin-bottom: 24px;
}

.settings-list {
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08); /* 提高边框对比度 */
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  display: flex;
  flex-direction: column;
}

.setting-name {
  font-size: 14.5px; /* 增大设置名称 */
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9); /* 提高对比度 */
  letter-spacing: 0.2px;
}

.setting-desc {
  font-size: 13px; /* 增大描述文本 */
  color: rgba(255, 255, 255, 0.7); /* 提高对比度 */
  margin-top: 4px;
  letter-spacing: 0.1px;
}

.setting-control button {
  background-color: #2c2c2c;
  border: 1px solid #3a3a3a;
  color: rgba(255, 255, 255, 0.9); /* 提高按钮文字对比度 */
  padding: 5px 12px; /* 增加按钮内边距 */
  border-radius: 4px;
  font-size: 13.5px; /* 增大按钮字体 */
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 450;
  letter-spacing: 0.2px;
}

.setting-control button:hover {
  background-color: #3a3a3a;
  border-color: #4a4a4a;
}

/* 现代开关样式 */
.toggle-switch-placeholder {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7); /* 提高对比度 */
}

.toggle-switch-placeholder .switch {
  width: 38px; /* 增大开关尺寸 */
  height: 20px; /* 增大开关尺寸 */
  background-color: #3a3a3a;
  border-radius: 10px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
}

.toggle-switch-placeholder .switch::before {
  content: '';
  position: absolute;
  width: 16px; /* 增大开关按钮尺寸 */
  height: 16px; /* 增大开关按钮尺寸 */
  border-radius: 50%;
  background-color: #ffffff;
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 模拟开关打开状态 */
.toggle-switch-placeholder.active .switch {
  background-color: #1890ff;
}

.toggle-switch-placeholder.active .switch::before {
  transform: translateX(18px);
}
</style> 