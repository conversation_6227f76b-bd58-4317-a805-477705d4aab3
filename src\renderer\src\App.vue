<script setup>
import { shallowRef } from 'vue';
import SidebarMenu from './components/SidebarMenu.vue';
import Dashboard from './components/Dashboard.vue';
import HotkeySettings from './components/HotkeySettings.vue';
import ScheduleTask from './components/ScheduleTask.vue';
import Settings from './components/Settings.vue';
import PythonScripts from './components/PythonScripts.vue';
import CmdScripts from './components/CmdScripts.vue';
import JsScripts from './components/JsScripts.vue';

// 使用 shallowRef 优化性能
const currentComponent = shallowRef(Dashboard);

const handleMenuItemClick = (menuId) => {
  switch (menuId) {
    case 'dashboard':
      currentComponent.value = Dashboard;
      break;
    case 'python-scripts':
      currentComponent.value = PythonScripts;
      break;
    case 'cmd-scripts':
      currentComponent.value = CmdScripts;
      break;
    case 'js-scripts':
      currentComponent.value = JsScripts;
      break;
    case 'hotkeys':
      currentComponent.value = HotkeySettings;
      break;
    case 'schedule':
      currentComponent.value = ScheduleTask;
      break;
    case 'settings':
      currentComponent.value = Settings;
      break;
    default:
      currentComponent.value = Dashboard;
  }
};
</script>

<template>
  <div class="app">
    <div class="sidebar">
      <SidebarMenu @menu-item-clicked="handleMenuItemClick" />
    </div>
    <div class="content">
      <component :is="currentComponent"></component>
    </div>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #121212;
  color: #fff;
  height: 100vh;
  overflow: hidden;
}

.app {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  width: 220px;
  height: 100%;
  background-color: #1e1e1e;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  flex-shrink: 0;
}

.content {
  flex-grow: 1;
  height: 100%;
  overflow-y: auto;
  background-color: #121212;
}
</style>
